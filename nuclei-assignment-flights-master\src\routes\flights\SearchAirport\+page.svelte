<script lang="ts">
    import { page } from '$app/stores';
    import { base } from '$app/paths';
    import { onMount, onDestroy } from 'svelte';
    import AppBar from '@CDNA-Technologies/svelte-vitals/components/appbar';
    import { NavigatorUtils } from '@CDNA-Technologies/svelte-vitals/navigator';
    import { ApiUtil } from '@CDNA-Technologies/svelte-vitals/api-util';
    import type { AirportSuggestionsResponse, AirportList, AirportSearchRequest } from '../path/to/your/messages';
    import { getPopularCities, type Airport } from './flights-api'; // Import the new service file

    let searchQuery = '';
    let recentSearches = [];
    let popularCities: Airport[] = []; // This will be populated from API
    let isLoadingPopular = true;
    let isSearching = false;
    let suggestedResponse: AirportSuggestionsResponse | undefined;
    let searchTimeout: NodeJS.Timeout;

    // Define Airport type to match your existing structure
    const allAirports: Airport[] = [
        { code: '<PERSON><PERSON>', name: 'Bangalore', airport: 'Kempegowda International Airport' },
    ];

    // API function for airport search
    async function getAirportSearchResults(searchText: string) {
        const airportSearchRequest: AirportSearchRequest = {
            searchText: searchText.trim()
        };
        return await ApiUtil.post<AirportSearchRequest, AirportSuggestionsResponse>(
            '/com.gonuclei.flights.v1.LandingService/getAirportSearchResults',
            airportSearchRequest
        );
    }

    // Debounced search function
    async function performSearch(query: string) {
        if (query.length > 2) {
            isSearching = true;
            try {
                let apiResponse = await getAirportSearchResults(query);
                if (apiResponse.hasError()) {
                    console.error('Search API error:', apiResponse.error);
                    suggestedResponse = undefined;
                } else {
                    suggestedResponse = apiResponse.response!;
                }
            } catch (error) {
                console.error('Search error:', error);
                suggestedResponse = undefined;
            }
            isSearching = false;
        } else {
            suggestedResponse = undefined;
        }
    }

    // Handle search input with debouncing
    function handleSearchInput(event: Event) {
        const target = event.target as HTMLInputElement;
        searchQuery = target.value;
        
        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }
        
        // Reset suggestions if query is too short
        if (searchQuery.length <= 2) {
            suggestedResponse = undefined;
            isSearching = false;
            return;
        }
        
        // Set new timeout for debounced search
        searchTimeout = setTimeout(() => {
            performSearch(searchQuery);
        }, 300); // 300ms debounce
    }

    // Get the selectionType from the URL state passed from the previous page
    $: isSourceSelection = $page.url.searchParams.has('source');
    $: isDestinationSelection = $page.url.searchParams.has('destination');
    $: selectionType = isSourceSelection ? 'Source' : isDestinationSelection ? 'Destination' : 'Source';
    $: heading = `Search ${selectionType} City`;

    // Filter airports based on the search query (for fallback/local search)
    $: filteredAirports = allAirports.filter(
        (airport) =>
            airport.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            airport.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
            airport.airport.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Convert API airport format to local Airport format
    function convertApiAirportToLocal(apiAirport: AirportList): Airport {
        return {
            code: apiAirport.iataCode,
            name: apiAirport.city,
            airport: apiAirport.name
        };
    }

    // Get airports to display based on search state
    $: airportsToShow = (() => {
        if (!searchQuery) return [];
        
        // If we have API results, use them
        if (suggestedResponse?.airportList) {
            return suggestedResponse.airportList.map(convertApiAirportToLocal);
        }
        
        // Fallback to local filtered results
        return filteredAirports.slice(0, 10);
    })();

    // Load popular cities on component mount
    onMount(async () => {
        console.log('Component mounted, loading popular cities...');
        isLoadingPopular = true;

        try {
            // Use the API service function to get data
            popularCities = await getPopularCities();
            console.log('Popular cities loaded:', popularCities);
        } catch (error) {
            console.error('Error loading popular cities:', error);
        } finally {
            isLoadingPopular = false;
        }
    });

    // Cleanup timeout on component destroy
    onDestroy(() => {
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }
    });

    // Function to handle when a city is clicked
    function handleCityClick(city: Airport) {
        console.log(`Selected city: ${city.name} for ${selectionType}`);
        
        NavigatorUtils.navigateTo({
            url: base + '/flights/landing',
            opts: {
                replaceState: false,
                state: { 
                    selectionType: selectionType, 
                    city: { city: city.name, airport: city.airport, code: city.code }
                }
            }
        });
    }
</script>

<div class="bg-white min-h-screen">
    <AppBar title={heading} />

    <div class="px-5 py-4 border-b border-gray-200">
        <div class="relative">
            <input
                type="text"
                placeholder="Enter City/Airport Name"
                value={searchQuery}
                on:input={handleSearchInput}
                class="w-full pl-4 pr-11 py-3 bg-gray-50 border border-gray-300 rounded-lg text-base placeholder-gray-400 focus:bg-white focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none transition-all duration-200"
            />
            <svg
                class="absolute right-3.5 top-1/2 transform -translate-y-1/2 text-gray-500"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
            >
                <circle cx="11" cy="11" r="8" />
                <path d="M21 21l-4.35-4.35" />
            </svg>
            
            <!-- Loading indicator -->
            {#if isSearching}
                <div class="absolute right-10 top-1/2 transform -translate-y-1/2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                </div>
            {/if}
        </div>
    </div>

    <!-- Conditional rendering for search results or default lists -->
    {#if searchQuery}
        <div class="max-h-96 overflow-y-auto border-b border-gray-200">
            {#if isSearching}
                <div class="flex justify-center items-center py-8">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span class="ml-2 text-gray-500">Searching...</span>
                </div>
            {:else if airportsToShow.length > 0}
                {#each airportsToShow as airport}
                    <button
                        class="w-full flex items-center py-3 px-5 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
                        on:click={() => handleCityClick(airport)}
                    >
                        <div
                            class="w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
                        >
                            {airport.code}
                        </div>
                        <div class="flex-1 min-w-0 text-left">
                            <div class="text-base font-medium text-gray-900 mb-0.5">{airport.name}</div>
                            <div class="text-xs text-gray-500 leading-snug">{airport.airport}</div>
                        </div>
                    </button>
                {/each}
            {:else if searchQuery.length > 2}
                <div class="flex justify-center items-center py-8">
                    <span class="text-gray-500">No airports found</span>
                </div>
            {/if}
        </div>
    {:else}
        <!-- Recent Searches Section -->
        {#if recentSearches.length > 0}
            <div class="px-5">
                <div class="flex items-center py-4 pb-3 border-b border-gray-100">
                    <svg
                        class="text-gray-500 mr-2"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                    >
                        <circle cx="12" cy="12" r="10" />
                        <polyline points="12,6 12,12 16,14" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500 uppercase tracking-wider">
                        Recent Searches
                    </span>
                </div>
                <div class="pb-2">
                    {#each recentSearches as airport}
                        <button
                            class="w-full flex items-center py-3 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
                            on:click={() => handleCityClick(airport)}
                        >
                            <div
                                class="w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
                            >
                                {airport.code}
                            </div>
                            <div class="flex-1 min-w-0 text-left">
                                <div class="text-base font-medium text-gray-900 mb-0.5">{airport.name}</div>
                                <div class="text-xs text-gray-500 leading-snug">{airport.airport}</div>
                            </div>
                        </button>
                    {/each}
                </div>
            </div>
        {/if}

        <!-- Popular Cities Section -->
        <div class="px-5">
            <div class="flex items-center py-4 pb-3 border-b border-gray-100">
                <svg
                    class="text-gray-500 mr-2"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                >
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                    <circle cx="12" cy="10" r="3" />
                </svg>
                <span class="text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Popular Cities
                </span>
            </div>
            
            {#if isLoadingPopular}
                <div class="flex justify-center items-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <span class="ml-2 text-gray-500">Loading popular cities...</span>
                </div>
            {:else}
                <div class="pb-2">
                    {#each popularCities as airport}
                        <button
                            class="w-full flex items-center py-3 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
                            on:click={() => handleCityClick(airport)}
                        >
                            <div
                                class="w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
                            >
                                {airport.code}
                            </div>
                            <div class="flex-1 min-w-0 text-left">
                                <div class="text-base font-medium text-gray-900 mb-0.5">{airport.name}</div>
                                <div class="text-xs text-gray-500 leading-snug">{airport.airport}</div>
                            </div>
                        </button>
                    {/each}
                </div>
            {/if}
        </div>
    {/if}
</div>
# Default values for chart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  registry: nucleiregistry.azurecr.io
  repository: ''
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ''

imagePullSecrets: []
nameOverride: ''
fullnameOverride: ''

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ''

podAnnotations:
  sidecar.istio.io/rewriteAppHTTPProbers: 'false'

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

secretProviders: []

configs: {}

service:
  type: ClusterIP
  port: 3000

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1000m
    memory: 128Mi
  requests:
    cpu: 100m
    memory: 64Mi

readinessProbe:
  failureThreshold: 3
  httpGet:
    path: ''
    port: 3000
    scheme: HTTP
  initialDelaySeconds: 20
  periodSeconds: 5
  successThreshold: 2
  timeoutSeconds: 4

istio:
  enabled: true
  gateways:
    - 'mwa-nuclei-gateway'
  hosts:
    - 'istio-mwa.internal.com'
  gateway:
    selector: ingressgateway
  match:
    uri:
      - format: prefix
        value: ''
        rewrite: ''
    headers:
      - name: user-agent
        format: regex
        value: '^.*(iPad|iPhone|Macintosh).*(OS 11|Version\/11).*$'
  destination:
    safari11:
      host: ''

autoscaling: {}

nodeSelector: {}

tolerations: []

affinity: {}

<!--
  Category Developers: Do not edit this file. It is automatically generated from the source code.
-->
<script lang="ts">
	import {
		RootPage,
		ErrorPage,
		Layout,
		LayoutUtils
	} from '@CDNA-Technologies/svelte-vitals/routes-pages';
	import { DfpAdBanner } from '@CDNA-Technologies/svelte-vitals/ad-banners';
	import { ApiUtil, type DataOrError } from '@CDNA-Technologies/svelte-vitals/api-util';
	import {
		WalletCouponSection,
		CartPriceDetailsWithCta,
		ReviewingCartModal,
		CartOperationErrorStore,
		CartReviewApiStore,
		ReviewingCartMessageStore,
		ReviewingCartStore
	} from '@CDNA-Technologies/svelte-vitals/cart';
	import {
		AppliedCouponDetails,
		ApplyCouponCta,
		CouponPage,
		ListCouponCta,
		CouponAppliedStore,
		CouponApiStore,
		IsApplyingCouponStore,
		CouponsApi
	} from '@CDNA-Technologies/svelte-vitals/cart/coupons';
	import {
		IsApplyingWalletStore,
		WalletAppliedApiStore,
		WalletUnAppliedApiStore,
		WalletApi,
		LandingWalletCta
	} from '@CDNA-Technologies/svelte-vitals/cart/wallet';
	import { FareDetails } from '@CDNA-Technologies/svelte-vitals/cart/fare-details';
	import {
		setLoadingLce,
		setContentLce,
		setErrorLce,
		lceStore,
		type LCE,
		ErrorHandling
	} from '@CDNA-Technologies/svelte-vitals/error-handling';
	import AppBar from '@CDNA-Technologies/svelte-vitals/components/appbar';
	import {
		BottomSheet,
		openBottomSheet,
		closeBottomSheet,
		bottomSheetLceStore,
		BottomSheetLCE,
		isBottomSheetOpen,
		isBottomSheetOpenAndOnTop,
		addInBottomSheetStore,
		isAnyBottomSheetPresent,
		setErrorBottomSheetLce,
		setContentBottomSheetLce,
		setLoadingBottomSheetLce
	} from '@CDNA-Technologies/svelte-vitals/components/bottom-sheet';
	import ConfirmationDialog from '@CDNA-Technologies/svelte-vitals/components/confirmation-dialog';
	import ErrorView from '@CDNA-Technologies/svelte-vitals/components/error-view';
	import IconButton from '@CDNA-Technologies/svelte-vitals/components/icon-button';
	import Image from '@CDNA-Technologies/svelte-vitals/components/image';
	import {
		type Item,
		ItemBuilder,
		KeyValueMapBuilder
	} from '@CDNA-Technologies/svelte-vitals/components/item-attribute';
	import LazyLoadList from '@CDNA-Technologies/svelte-vitals/components/lazy-load-list';
	import { List } from '@CDNA-Technologies/svelte-vitals/components/list';
	import Loader from '@CDNA-Technologies/svelte-vitals/components/loader';
	import LoaderDialog from '@CDNA-Technologies/svelte-vitals/components/loader-dialog';
	import PoweredByNucleiFooter from '@CDNA-Technologies/svelte-vitals/components/powered-by-nuclei-footer';
	import PrimaryButton from '@CDNA-Technologies/svelte-vitals/components/primary-button';
	import PrimaryLoader from '@CDNA-Technologies/svelte-vitals/components/primary-loader';
	import RadioList from '@CDNA-Technologies/svelte-vitals/components/radio-list';
	import SearchBar from '@CDNA-Technologies/svelte-vitals/components/search-bar';
	import SecondaryButton from '@CDNA-Technologies/svelte-vitals/components/secondary-button';
	import SoldByVendor from '@CDNA-Technologies/svelte-vitals/components/sold-by-vendor';
	import Tab from '@CDNA-Technologies/svelte-vitals/components/tab';
	import TabView from '@CDNA-Technologies/svelte-vitals/components/tabview';
	import TextFieldError from '@CDNA-Technologies/svelte-vitals/components/text-field-error';
	import TextInputField from '@CDNA-Technologies/svelte-vitals/components/text-input-field';
	import ThreeDotMenu from '@CDNA-Technologies/svelte-vitals/components/three-dot-menu';
	import {
		HOST_NAME,
		_getAuthToken,
		_getDatePickerStyle,
		_getExtraHeaders,
		_getPartnerCouponEnabled,
		_getPartnerMenu,
		_getPartnerWalletEnabled,
		_getStyle,
		_getUserDetails,
		BASE_THEME
	} from '@CDNA-Technologies/svelte-vitals/constants';
	import {
		TECHNICAL_ERROR,
		NO_INTERNET,
		NO_TRANSACTION,
		COUPONS,
		CANCELLED_ITEM
	} from '@CDNA-Technologies/svelte-vitals/image-constants';
	import {
		CountryList,
		CountryCodeCountryListHeader,
		NationalityCountryListHeader,
		PassportCountryListHeader,
		countryResponseStore,
		countrySelectedStoreForCode,
		countrySelectedStoreForNationality,
		countrySelectedStoreForPassport
	} from '@CDNA-Technologies/svelte-vitals/country-list';
	import { NucleiLogger } from '@CDNA-Technologies/svelte-vitals/logger';
	import type {
		ResponseCodeCause,
		ResponseCode,
		ResponseStatus,
		ErrorHandlingDetail,
		ToastError,
		ToastType,
		BottomOverlayError,
		ScreenType,
		CtaButton,
		CtaAction,
		ButtonType,
		UserWalletRequest,
		UserWalletResponse,
		Response,
		ApplyWalletRequest,
		ApplyWalletResponse,
		UnApplyWalletRequest,
		UnApplyWalletResponse,
		WalletInfo,
		WalletDetails,
		WalletDetails,
		DisplayInfo,
		ErrorHandlingDetails,
		CtaButton,
		CtaAction,
		RemoveCouponRequest,
		RemoveCouponResponse,
		ListCouponsResponse,
		CouponGroup,
		CouponData,
		CouponInfo,
		Status,
		ListCouponsRequest,
		ApplyCouponsRequest,
		ApplyCouponsResponse,
		LCE,
		CartReviewResponse,
		CartData,
		CartItem,
		PriceDetails,
		AdditionalCharge,
		CartAttribute,
		SegmentHeader
	} from '@CDNA-Technologies/svelte-vitals/messages';
	import {
		NavigatorErrorBottomSheet,
		NavigatorUtils
	} from '@CDNA-Technologies/svelte-vitals/navigator';
	import { getPaymentInitData } from '@CDNA-Technologies/svelte-vitals/payment';
	import {
		UniversalTravellerProfile,
		tDetailsTFControllerStore,
		type UniversalTravellerConfig,
		type TravellerProfileResponse,
		type TravellerProfileResponseStatus,
		type TravellerProfile,
		type TravellerIdentityRequest,
		type TravellerIdentity,
		type TravellerProfile,
		type DeleteTravellerProfileRequest
	} from '@CDNA-Technologies/svelte-vitals/universal-traveller';
	import {
		deepCopy,
		StringUtils,
		hooksHandler,
		Category,
		categoryMapper,
		readiness
	} from '@CDNA-Technologies/svelte-vitals/util';
	import {
		getContacts,
		getHeaders,
		getPartnerMenuOptions,
		getPermissionStatus,
		getThemeData,
		askPermission,
		onAppInit,
		closeApp,
		fetchAdsFromDfp,
		openPermissionAppSetting,
		showToast,
		trackCustomEvent,
		bannerTrackImpression,
		onBannerClicked,
		clearBannerInNative,
		shareDocument,
		openHelpScreen,
		openPartnerPaymentScreen,
		downloadDocument,
		logout,
		openWebView,
		onPartnerMenuOptionClicked,
		openCalendar
	} from '@CDNA-Technologies/svelte-vitals/native-bridge-util';
	import { NucleiAnalyticsEvent, NucleiEvent } from '@CDNA-Technologies/svelte-vitals/analytics';
	import { parsePathParam } from '@CDNA-Technologies/svelte-vitals/url-util';
	import {
		hexToHSL,
		getDefaultThemeStyle,
		getStyleFromTheme
	} from '@CDNA-Technologies/svelte-vitals/theme-util';
</script>

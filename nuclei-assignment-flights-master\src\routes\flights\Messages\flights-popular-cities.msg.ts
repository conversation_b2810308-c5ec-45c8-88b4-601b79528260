export interface AirportSuggestionsResponse {
    status:      Status;
    title:       string;
    airportList: AirportList[];
}

export interface AirportList {
    iataCode:    string;
    city:        string;
    name:        string;
    countryCode: string;
    iconUrl:     string;
}

export interface Status {
    responseCode:      string;
    responseCodeCause: string;
    responseMessage:   string;
}

export interface AirportSuggestionsRequest {}
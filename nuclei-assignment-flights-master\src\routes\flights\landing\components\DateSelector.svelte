<script lang="ts">
	import { createEventDispatcher } from 'svelte';

	export let departureDate;
	export let departureDay;
	export let returnDate;
	export let isReturnTrip;
	export let showCalendar;
	export let selectedDate;

	const dispatch = createEventDispatcher();

	function openCalendar() {
		dispatch('openCalendar');
	}

	function toggleReturnTrip() {
		dispatch('toggleReturn');
	}

	function handleCalendarSelect(event) {
		dispatch('calendarSelect', event);
	}

	function handleCalendarClose(event) {
		dispatch('calendarClose', event);
	}
</script>

<!-- Departure and Return Dates -->
<div class="relative">
	<div class="w-full flex bg-white border border-gray-200 rounded-lg shadow-sm text-left mb-4">
		<!-- Make sure this button container has relative positioning -->
		<div class="flex-1 relative">
			<button class="w-full p-4 px-4 bg-white rounded-lg shadow-sm text-left hover:bg-gray-50 transition-colors duration-200" on:click|stopPropagation={openCalendar}>
				<div class="flex items-center">
					<div>
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
							<path d="M2 22.5H22" stroke="#8A8A8A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							<path d="M3.77 11.27L2 9.5L4 5L5.1 5.55C5.65 5.83 6 6.39 6 7C6 7.61 6.35 8.17 6.9 8.45L8 9L11 3L12.05 3.53C12.3418 3.67534 12.5937 3.88981 12.7836 4.15473C12.9736 4.41965 13.096 4.72699 13.14 5.05L13.86 10.45C13.904 10.773 14.0264 11.0804 14.2164 11.3453C14.4063 11.6102 14.6582 11.8247 14.95 11.97L19.35 14.17C19.77 14.39 20.13 14.72 20.36 15.13L20.96 16.16C21.45 17.04 20.9 18.14 19.9 18.26L18.72 18.41C18.25 18.47 17.77 18.39 17.35 18.17L4.29 11.65C4.09728 11.5523 3.92167 11.4239 3.77 11.27Z" stroke="#8A8A8A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</div>
					<div>
						<div class="text-gray-500 pl-3 text-xs">Departure</div>
						<div class="text-gray-900 pl-3 font-bold text-lg">{departureDate}</div>
						<div class="text-gray-500 pl-3 text-xs">{departureDay}</div>
					</div>
				</div>
			</button>

			<!-- Calendar Component - positioned relative to this container -->
			<slot></slot>
		</div>

		<div class="border-l border-gray-200 my-2"></div> <!-- Vertical divider -->
		
		<button class="flex-1 p-4 bg-white rounded-lg shadow-sm text-left px-4 hover:bg-gray-50 transition-colors duration-200" on:click={toggleReturnTrip}>
			<div class="flex items-center">
				<div>
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
						<path d="M18 21C18 18.8783 17.1571 16.8434 15.6569 15.3431C14.1566 13.8429 12.1217 13 10 13C7.87827 13 5.84344 13.8429 4.34315 15.3431C2.84285 16.8434 2 18.8783 2 21" stroke="#8A8A8A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M10 13C12.7614 13 15 10.7614 15 8C15 5.23858 12.7614 3 10 3C7.23858 3 5 5.23858 5 8C5 10.7614 7.23858 13 10 13Z" stroke="#8A8A8A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M22.0008 19.9992C22.0008 16.6292 20.0008 13.4992 18.0008 11.9992C18.6582 11.506 19.1839 10.8583 19.5313 10.1135C19.8788 9.36867 20.0373 8.54969 19.9928 7.72902C19.9483 6.90835 19.7022 6.1113 19.2763 5.40842C18.8503 4.70553 18.2577 4.11848 17.5508 3.69922" stroke="#8A8A8A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
					</svg>
				</div>
				<div>
					<div class="text-gray-500 pl-3 text-xs">Return</div>
					{#if isReturnTrip && returnDate}
						<div class="text-gray-900 pl-3 font-bold text-lg">{returnDate}</div>
						<div class="text-gray-500 pl-3 text-xs">sunday</div>
					{:else}
						<div class="text-blue-600 pl-3 font-bold text-lg">Add Return</div>
						<div class="text-gray-500 pl-3 text-xs">and save more!</div>
					{/if}
				</div>
			</div>
		</button>
	</div>
</div>
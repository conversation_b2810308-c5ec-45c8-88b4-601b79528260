<script lang="ts">
    import { page } from '$app/stores';
    import { base } from '$app/paths';
    import { onMount } from 'svelte';
    import AppBar from '@CDNA-Technologies/svelte-vitals/components/appbar';
    import { NavigatorUtils } from '@CDNA-Technologies/svelte-vitals/navigator';
    import { ApiUtil, DataOrError } from '@CDNA-Technologies/svelte-vitals/api-util';
    
    
    //import { NucleiNavigatorStore } from '@CDNA-Technologies/svelte-vitals/navigator/store';

    let searchQuery = '';
    let recentSearches = [];

    const popularCities = [
        { code: 'BLR', name: 'Bangalore', airport: 'Kempegowda International Airport' },
        { code: 'DEL', name: 'New Delhi', airport: 'Indira Gandhi International Airport' },
        { code: 'MUM', name: 'Mumbai', airport: 'Chhatrapati Shivaji Maharaj International Airport' },
        { code: 'PAT', name: 'Patna', airport: 'Lok Nayak Jayaprakash Airport' },
        { code: 'HYD', name: 'Hyderabad', airport: 'Rajiv Gandhi International Airport' },
        { code: 'PNQ', name: 'Pune', airport: 'Pune Airport' }
    ];

    const allAirports = [
        { code: 'BLR', name: 'Bangalore', airport: 'Kempegowda International Airport' },
        { code: 'DEL', name: 'New Delhi', airport: 'Indira Gandhi International Airport' },
        { code: 'MUM', name: 'Mumbai', airport: 'Chhatrapati Shivaji Maharaj International Airport' },
        { code: 'CCU', name: 'Kolkata', airport: 'Netaji Subhas Chandra Bose International Airport' },
        { code: 'HYD', name: 'Hyderabad', airport: 'Rajiv Gandhi International Airport' },
        { code: 'PNQ', name: 'Pune', airport: 'Pune Airport' },
        { code: 'AMD', name: 'Ahmedabad', airport: 'Sardar Vallabhbhai Patel International Airport' },
        { code: 'MAA', name: 'Chennai', airport: 'Chennai International Airport' },
        { code: 'COK', name: 'Kochi', airport: 'Cochin International Airport' },
        { code: 'GOI', name: 'Goa', airport: 'Goa International Airport' },
        { code: 'JAI', name: 'Jaipur', airport: 'Jaipur International Airport' },
        { code: 'LKO', name: 'Lucknow', airport: 'Chaudhary Charan Singh International Airport' },
        { code: 'IXC', name: 'Chandigarh', airport: 'Chandigarh Airport' },
        { code: 'VNS', name: 'Varanasi', airport: 'Lal Bahadur Shastri Airport' },
        { code: 'IXB', name: 'Bagdogra', airport: 'Bagdogra Airport' },
        { code: 'GAU', name: 'Guwahati', airport: 'Lokpriya Gopinath Bordoloi International Airport' },
        { code: 'BBI', name: 'Bhubaneswar', airport: 'Biju Patnaik International Airport' },
        { code: 'CTC', name: 'Cuttack', airport: 'Cuttack Airport' },
        { code: 'IXR', name: 'Ranchi', airport: 'Birsa Munda Airport' },
        { code: 'PAT', name: 'Patna', airport: 'Jay Prakash Narayan Airport' }
    ];

    // Get the selectionType from the URL state passed from the previous page
    let selectionType = $page.state?.selectionType || 'Source';
    let heading = Search ${selectionType} City;

    // Filter airports based on the search query
    $: filteredAirports = allAirports.filter(
        (airport) =>
            airport.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            airport.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
            airport.airport.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Function to handle when a city is clicked
    function handleCityClick(city) {
        console.log(Selected city: ${city.name} for ${selectionType});
        
        // Use your custom navigator to go back to the landing page,
        // and pass the selected city and the selection type in the state.
        NavigatorUtils.navigateTo({
            url: base+'/flights/landing',
            opts: {
                replaceState: true, // Replace the history entry so the back button goes to the previous page
                state: { 
                    selectionType: selectionType, 
                    city: { city: city.name, airport: city.airport, code: city.code }
                }
            }
        });
    }
    let airports: Airport[] = [];

</script>

<div class="bg-white min-h-screen">
    <!-- AppBar with dynamic title -->
     <div class="h-screen flex flex-col bg-gray-50">
	<h2 class="text-xl font-semibold mb-4">Popular Airports</h2>
	<ul class="space-y-2">
		{#each airports as airport}
			<li class="border p-2 rounded">
				<strong>{airport.city}</strong> ({airport.iataCode}) - {airport.name}
			</li>
		{/each}
	</ul>
</div>
    <AppBar title={heading} />

    <div class="px-5 py-4 border-b border-gray-200">
        <div class="relative">
            <input
                type="text"
                placeholder="Enter City/Airport Name"
                bind:value={searchQuery}
                class="w-full pl-4 pr-11 py-3 bg-gray-50 border border-gray-300 rounded-lg text-base placeholder-gray-400 focus:bg-white focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none transition-all duration-200"
            />
            <svg
                class="absolute right-3.5 top-1/2 transform -translate-y-1/2 text-gray-500"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
            >
                <circle cx="11" cy="11" r="8" />
                <path d="M21 21l-4.35-4.35" />
            </svg>
        </div>
    </div>

    <!-- Conditional rendering for search results or default lists -->
    {#if searchQuery}
        <div class="max-h-96 overflow-y-auto border-b border-gray-200">
            {#each filteredAirports.slice(0, 10) as airport}
                <button
                    class="w-full flex items-center py-3 px-5 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
                    on:click={() => handleCityClick(airport)}
                >
                    <div
                        class="w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
                    >
                        {airport.code}
                    </div>
                    <div class="flex-1 min-w-0 text-left">
                        <div class="text-base font-medium text-gray-900 mb-0.5">{airport.name}</div>
                        <div class="text-xs text-gray-500 leading-snug">{airport.airport}</div>
                    </div>
                </button>
            {/each}
        </div>
    {:else}
        <!-- Recent Searches Section -->
        {#if recentSearches.length > 0}
            <div class="px-5">
                <div class="flex items-center py-4 pb-3 border-b border-gray-100">
                    <svg
                        class="text-gray-500 mr-2"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                    >
                        <circle cx="12" cy="12" r="10" />
                        <polyline points="12,6 12,12 16,14" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500 uppercase tracking-wider">
                        Recent Searches
                    </span>
                </div>
                <div class="pb-2">
                    {#each recentSearches as airport}
                        <button
                            class="w-full flex items-center py-3 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
                            on:click={() => handleCityClick(airport)}
                        >
                            <div
                                class="w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
                            >
                                {airport.code}
                            </div>
                            <div class="flex-1 min-w-0 text-left">
                                <div class="text-base font-medium text-gray-900 mb-0.5">{airport.name}</div>
                                <div class="text-xs text-gray-500 leading-snug">{airport.airport}</div>
                            </div>
                        </button>
                    {/each}
                </div>
            </div>
        {/if}

        <!-- Popular Cities Section -->
        <div class="px-5">
            <div class="flex items-center py-4 pb-3 border-b border-gray-100">
                <svg
                    class="text-gray-500 mr-2"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                >
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                    <circle cx="12" cy="10" r="3" />
                </svg>
                <span class="text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Popular Cities
                </span>
            </div>
            <div class="pb-2">
                {#each popularCities as airport}
                    <button
                        class="w-full flex items-center py-3 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
                        on:click={() => handleCityClick(airport)}
                    >
                        <div
                            class="w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
                        >
                            {airport.code}
                        </div>
                        <div class="flex-1 min-w-0 text-left">
                            <div class="text-base font-medium text-gray-900 mb-0.5">{airport.name}</div>
                            <div class="text-xs text-gray-500 leading-snug">{airport.airport}</div>
                        </div>
                    </button>
                {/each}
            </div>
        </div>
    {/if}
</div>
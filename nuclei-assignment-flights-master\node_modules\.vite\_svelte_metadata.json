{"compilerOptions": {"css": "external", "dev": true, "accessors": false, "hydratable": true}, "configFile": "D:\\nuclei-assignment-flights-master\\nuclei-assignment-flights-master\\svelte.config.js", "extensions": [".svelte"], "preprocess": [{"defaultLanguages": {"markup": "html", "style": "css", "script": "javascript"}, "markup": "async ({ content, filename }) => {\r\n        if (transformers.replace) {\r\n            const transformed = await (0, exports.transform)('replace', transformers.replace, {\r\n                content,\r\n                markup: content,\r\n                filename,\r\n            });\r\n            content = transformed.code;\r\n        }\r\n        return (0, markup_1.transformMarkup)({ content, filename }, markupTransformer, {\r\n            // we only pass the markupTagName because the rest of options\r\n            // is fetched internally by the `markupTransformer`\r\n            markupTagName,\r\n        });\r\n    }", "script": "async ({ content, attributes, markup: fullMarkup, filename, }) => {\r\n        const transformResult = await scriptTransformer({\r\n            content,\r\n            attributes,\r\n            markup: fullMarkup,\r\n            filename,\r\n        });\r\n        let { code, map, dependencies, diagnostics } = transformResult;\r\n        if (transformers.babel) {\r\n            const transformed = await (0, exports.transform)('babel', getTransformerOptions('babel'), { content: code, markup: fullMarkup, map, filename, attributes });\r\n            code = transformed.code;\r\n            map = transformed.map;\r\n            dependencies = (0, utils_1.concat)(dependencies, transformed.dependencies);\r\n            diagnostics = (0, utils_1.concat)(diagnostics, transformed.diagnostics);\r\n        }\r\n        return { code, map, dependencies, diagnostics };\r\n    }", "style": "async ({ content, attributes, markup: fullMarkup, filename, }) => {\r\n        const transformResult = await cssTransformer({\r\n            content,\r\n            attributes,\r\n            markup: fullMarkup,\r\n            filename,\r\n        });\r\n        let { code, map, dependencies } = transformResult;\r\n        const hasPostcss = await (0, utils_1.hasDepInstalled)('postcss');\r\n        // istanbul ignore else\r\n        if (hasPostcss) {\r\n            if (transformers.postcss) {\r\n                const { alias, lang } = (0, language_1.getLanguage)(attributes);\r\n                const postcssOptions = getTransformerOptions('postcss', (0, language_1.isAliasOf)(alias, lang) ? alias : null, \r\n                // todo: this seems wrong and ugly\r\n                { ignoreAliasOverride: true });\r\n                const transformed = await (0, exports.transform)('postcss', postcssOptions, {\r\n                    content: code,\r\n                    markup: fullMarkup,\r\n                    map,\r\n                    filename,\r\n                    attributes,\r\n                });\r\n                code = transformed.code;\r\n                map = transformed.map;\r\n                dependencies = (0, utils_1.concat)(dependencies, transformed.dependencies);\r\n            }\r\n            const transformed = await (0, exports.transform)('globalStyle', getTransformerOptions('globalStyle'), { content: code, markup: fullMarkup, map, filename, attributes });\r\n            code = transformed.code;\r\n            map = transformed.map;\r\n        }\r\n        else if ('global' in attributes) {\r\n            console.warn(`[svelte-preprocess] 'global' attribute found, but 'postcss' is not installed. 'postcss' is used to walk through the CSS and transform any necessary selector.`);\r\n        }\r\n        return { code, map, dependencies };\r\n    }"}]}
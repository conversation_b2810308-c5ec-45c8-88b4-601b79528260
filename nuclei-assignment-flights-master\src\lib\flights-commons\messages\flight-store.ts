// stores/flightStore.ts
import { writable } from 'svelte/store';

interface FlightSearchData {
    departure: string;
    passengers: number;
    clas: string;
    isReturnTrip: boolean;
    returnDate: string;
    showNonStop: boolean;
    specialFare: string;
}

interface FlightStore {
    searchData: FlightSearchData | null;
    selectedCities: any;
}

const initialState: FlightStore = {
    searchData: null,
    selectedCities: null
};

export const flightStore = writable(initialState);

// Helper functions for updating store
export const flightActions = {
    setSearchData: (data: FlightSearchData) => {
        flightStore.update(state => ({
            ...state,
            searchData: data
        }));
    },

    setSelectedCities: (cities: any) => {
        flightStore.update(state => ({
            ...state,
            selectedCities: cities
        }));
    },

    resetStore: () => {
        flightStore.set(initialState);
    }
};
{"name": "nuclei-assignment-flights", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "build:prod": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json --watch", "test": "playwright test", "vitest": "vitest", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write .", "coverage": "vitest run --coverage"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@playwright/test": "^1.25.0", "@sveltejs/adapter-auto": "1.0.0-next.80", "@sveltejs/adapter-node": "1.0.0-next.101", "@sveltejs/kit": "1.0.0-next.507", "@testing-library/jest-dom": "^5.16.5", "@testing-library/svelte": "^3.2.2", "@types/jest": "^29.2.0", "@vitest/coverage-c8": "^0.24.5", "autoprefixer": "^10.4.12", "c8": "^7.12.0", "eslint": "^8.16.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-svelte3": "^4.0.0", "jsdom": "^20.0.1", "postcss": "^8.4.17", "prettier": "^2.6.2", "prettier-plugin-svelte": "^2.7.0", "svelte": "^3.44.0", "svelte-calendar": "^3.1.6", "svelte-check": "^2.7.1", "svelte-inview": "^3.0.1", "svelte-preprocess": "^4.10.7", "svelte-time": "^0.7.1", "tailwindcss": "3.1.8", "tailwindcss-flip": "1.0.0", "typescript": "^4.7.4", "vite": "^3.1.0", "vitest": "^0.24.0"}, "type": "module", "dependencies": {"daisyui": "2.31.0", "dayjs": "^1.11.6", "dsbridge": "^3.1.4", "hex-to-hsl": "^1.0.0", "log-symbols": "^5.1.0", "platform": "^1.3.6", "@CDNA-Technologies/svelte-vitals": "0.0.122"}}
// utils/cityUtils.ts
export const cityUtils = {
    getInitialCities() {
        if (typeof window !== 'undefined') {
            const stored = sessionStorage.getItem('flightSelectedCities');
            if (stored) {
                try {
                    return JSON.parse(stored);
                } catch (e) {
                    console.error('Error parsing stored cities:', e);
                }
            }
        }
        
        // Default values if nothing stored
        return {
            from: { 
                city: 'Bangalore', 
                code: 'BLR', 
                airport: 'Bangalore International Airport' 
            },
            to: { 
                city: 'New Delhi', 
                code: 'DEL', 
                airport: 'Indira Gandhi International Airport' 
            }
        };
    },

    saveSelectedCities(selectedCities: any) {
        if (typeof window !== 'undefined') {
            sessionStorage.setItem('flightSelectedCities', JSON.stringify(selectedCities));
        }
    }
};
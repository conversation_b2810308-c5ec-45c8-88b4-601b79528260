<script lang="ts">
    import AppBar from "@CDNA-Technologies/svelte-vitals/components/appbar";
    import PrimaryLoader from "@CDNA-Technologies/svelte-vitals/components/primary-loader";
    import {
      ErrorHandling,
      lceStore,
      setContentLce,
      setLoadingLce,
      setErrorLce,
    } from "@CDNA-Technologies/svelte-vitals/error-handling";
    import { NucleiLogger } from "@CDNA-Technologies/svelte-vitals/logger";
    import { page } from '$app/stores';
    import { onMount } from "svelte";
    import dayjs from 'dayjs';
    import { ApiUtil } from '@CDNA-Technologies/svelte-vitals/api-util';

    // Get source and destination from page state or set defaults
    let fromCity = $page.state?.fromCity || { city: 'New Delhi', code: 'DEL' };
    let toCity = $page.state?.toCity || { city: 'Mumbai', code: 'BOM' };
    let travelDate = $page.state?.travelDate || '22 Dec';
    let travellers = $page.state?.travellers || 1;
    let classType = $page.state?.classType || 'Economy';

    console.log("data from Landing page: ", history.state);
    const state = history.state;
    const source = state?.searchData?.from?.city || 'Hyderabad';
    const dest = state?.searchData?.to?.city || 'Mumbai';
    const sourceCode = state?.searchData?.from?.code || 'HYD';
    const destCode = state?.searchData?.to?.code || 'BOM';
    let appBarTitle = `(${sourceCode}) ${source} → (${destCode}) ${dest}`;

    // Ensure we always use a valid future date
    const depart = (() => {
        const providedDate = state?.searchData?.departure;
        if (providedDate) {
            const parsedDate = dayjs(providedDate);
            // Check if the date is valid and in the future
            if (parsedDate.isValid() && parsedDate.isAfter(dayjs(), 'day')) {
                return providedDate;
            }
        }
        // Fallback to tomorrow's date if no valid future date is provided
        return dayjs().add(1, 'day').format('YYYY-MM-DD');
    })();
    const pass = state?.searchData?.passengers || 1;
    const clas = state?.searchData?.class || 'Business Class';

    let appBarSubtitle = `${depart} | ${pass} Traveller${pass > 1 ? 's' : ''} | ${clas}`;

    console.log("Data From Landing Page: ", source, dest);

    // Initialize with empty flights array - will be populated by API
    let flights = [];

    // Date options for the date selector
    let dateOptions = [
        { day: 'Tue, 27 Jun', price: 4807 },
        { day: 'Wed, 28 Jun', price: 5203 },
        { day: 'Thu, 29 Jun', price: 6100 },
        { day: 'Fri, 30 Ju', price: 4500 }
    ];

    let selectedDateIndex = 1; // Wed is selected by default
    let sortBy = 'cheapest';
    let showRefundableOnly = true;

    function handleSearchData(){
        const state = history.state;
        console.log('Navigation state:', state);
    }

    let searchData = $page.state?.searchData || {
        from: { 
            city: 'Hyderabad', 
            code: 'HYD',
            airport: 'Rajiv Gandhi International Airport' 
        },
        to: { 
            city: 'Mumbai', 
            code: 'BOM',
            airport: 'Chhatrapati Shivaji International Airport' 
        },
        departure: '15-08-2025',
        passengers: 1,
        class: 'Business Class'
    };

    // Helper function to get future date in DD-MM-YYYY format
    const getFutureDate = (): string => {
        const future = new Date();
        future.setDate(future.getDate() + 7); // Add 7 days to be safe
        const day = String(future.getDate()).padStart(2, '0');
        const month = String(future.getMonth() + 1).padStart(2, '0');
        const year = future.getFullYear();
        return `${day}-${month}-${year}`;
    };

    // Helper function to format date for API (API expects DD-MM-YYYY format)
    const formatDateForAPI = (dateString: string): string => {
        try {
            // If already in DD-MM-YYYY format, return as is
            if (dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                return dateString;
            }
            
            // If in YYYY-MM-DD format, convert to DD-MM-YYYY
            if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                const [year, month, day] = dateString.split('-');
                return `${day}-${month}-${year}`;
            }
            
            // If in other formats, try to parse and format to DD-MM-YYYY
            const date = new Date(dateString);
            if (!isNaN(date.getTime())) {
                const day = String(date.getDate()).padStart(2, '0');
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const year = date.getFullYear();
                return `${day}-${month}-${year}`;
            }
            
            // Fallback to a future date if parsing fails
            return getFutureDate();
        } catch (error) {
            console.error('Date formatting error:', error);
            return getFutureDate();
        }
    };

    // Helper function to generate flight tags based on flight data
    const generateFlightTags = (flight: any): string[] => {
        const tags = [];
        
        if (flight.fareRuleInformation?.cancellationPolicy) {
            tags.push('Refundable');
        }
        
        if (flight.fareRuleInformation?.meal || flight.amenities?.includes('meal')) {
            tags.push('Free Meal');
        }
        
        if (flight.fareRuleInformation?.baggage || flight.baggageInformation) {
            tags.push('Extra Baggage');
        }
        
        if (flight.stops === 0) {
            tags.push('Non-Stop');
        }
        
        return tags.slice(0, 2); // Limit to 2 tags to match your UI
    };

    // Transform API response to match your existing flight card structure
    const transformApiResponse = (apiData: any) => {
        try {
            console.log("Transforming API data:", apiData);
            
            // Check if we have onward flights in the response
            if (apiData.onwardFlights && Array.isArray(apiData.onwardFlights)) {
                return apiData.onwardFlights.map((flight: any, index: number) => ({
                    id: flight.flightId || index + 1,
                    airline: flight.airlineName || flight.marketingAirline?.airlineName || 'Unknown Airline',
                    logo: flight.airlineLogo || flight.marketingAirline?.airlineLogo || 'https://via.placeholder.com/40x20/FF0000/FFFFFF?text=AA',
                    departure: flight.departureTime || flight.segments?.[0]?.departureTime || '00:00',
                    arrival: flight.arrivalTime || flight.segments?.[flight.segments?.length - 1]?.arrivalTime || '00:00',
                    departureAirport: flight.source || flight.segments?.[0]?.origin || sourceCode,
                    arrivalAirport: flight.destination || flight.segments?.[flight.segments?.length - 1]?.destination || destCode,
                    duration: flight.duration || flight.totalDuration || (flight.stops === 0 ? 'Non-Stop' : `${flight.stops} Stop${flight.stops > 1 ? 's' : ''}`),
                    price: Math.round(flight.totalFare || flight.grandTotal || flight.fare || 0),
                    originalPrice: flight.publishedFare ? Math.round(flight.publishedFare) : null,
                    offer: flight.fareRuleInformation?.cancellationPolicy ? 'Found ₹400 off - Tap to compare' : null,
                    tags: generateFlightTags(flight),
                    amenities: flight.amenities || []
                }));
            }
            
            // If no flights in response, return empty array
            console.warn("No onward flights found in API response");
            return [];
        } catch (error) {
            console.error('Error transforming API response:', error);
            return [];
        }
    };

    const fetchScreenData = async () => {
        try {
            // Prepare the API request payload based on your curl command
            const apiPayload = {
                src: {
                    iataCode: sourceCode,
                    city: source,
                    countryCode: "IN"
                },
                des: {
                    iataCode: destCode,
                    city: dest,
                    countryCode: "IN",
                    fareType: "regular",
                    partnerCountry: "IN"
                },
                departDate: dayjs(depart).format("YYYY-MM-DD"), // Format date correctly in DD-MM-YYYY
                passenger: {
                    adultCount: pass || 1,
                    childCount: 0,
                    infantCount: 0
                },
                travellerClass: {
                    key: clas === 'Business Class' ? "BUSINESS" : (clas === 'Economy Class' ? "ECONOMY" : "PREMIUM"),
                    value: clas
                }
            };

            console.log("API Payload:", apiPayload);

            // API call using ApiUtil - only pass the path, not full URL
            // ApiUtil will automatically add HOST_NAME prefix
            const response = await ApiUtil.post(
                "/com.gonuclei.flights.v1.ListingService/GetFlightsSearchListV2",
                apiPayload
            );

            console.log({
        totalFlights: response.response.onwardFlights.length,
        isRoundTrip: response.response.isRoundTrip,
        isInternational: response.response.isInternational,
      });
            // Handle DataOrError response - use hasError() method
            if (response.hasError()) {
                console.error("API Error:", response.error);
                throw new Error(response.error?.title || "API request failed");
            }

            if (response.response) {
                console.log("Raw API Response Data:", response.response);
                
                // Check if we have flight data
                if (response.response.onwardFlights && response.response.onwardFlights.length > 0) {
                    // Transform API response to match your flight card format
                    flights = transformApiResponse(response.response);
                    console.log("Transformed flights:", flights);
                } else {
                    console.warn("No flights found in API response");
                    flights = [];
                }
            } else {
                console.warn("No data in API response");
                flights = [];
            }

            setContentLce();
        } catch (error) {
            console.error('Error fetching flights:', error);
            NucleiLogger.logException(`Failed to fetch flights: ${error}`, "Flights");
            setErrorLce({
                title: "Failed to load flights",
                message: "Unable to fetch flight data. Please try again.",
                actionText: "Retry"
            });
        }
    };

    onMount(async () => {
        NucleiLogger.logInfo("Flights", "Listing screen mounted");
        console.log('Page state:', $page.state);
        console.log('From City:', fromCity);
        console.log('To City:', toCity);
        handleSearchData();
        setLoadingLce();
        await fetchScreenData();
    });

    function handleRetry() {
        setLoadingLce();
        fetchScreenData();
    }

    function handleDateSelect(index: number) {
        selectedDateIndex = index;
        // Optionally trigger new API call with selected date
    }

    function handleSortFilter() {
        // Handle sort & filter functionality
        console.log('Sort & Filter clicked');
    }

    function toggleRefundable() {
        showRefundableOnly = !showRefundableOnly;
    }

    function handleFlightSelect(flight: any) {
        console.log('Selected flight:', flight);
        // Navigate to flight details or booking
    }
</script>

<div class="h-screen flex flex-col bg-gray-50">
    <!-- <AppBar title={appBarTitle} /> -->
     <AppBar>
	<div slot="title" class="flex flex-col leading-tight">
		<p class="pl-5 text-base font-bold">{appBarTitle}</p>
		<p class="pl-5 text-sm text-black-400">{appBarSubtitle}</p>
	</div>
</AppBar>

    {#if $lceStore.isLoading}
        <div class="h-screen flex flex-col justify-center">
            <PrimaryLoader />
        </div>
    {:else if $lceStore.hasError && $lceStore.errorDetails != null}
        <ErrorHandling
            errorHandling={$lceStore.errorDetails}
            on:submit={handleRetry}
        />
    {:else if $lceStore.hasContent}
        <div class="flex-1 overflow-hidden">
            <!-- Date Selector -->
            <div class="bg-white px-4 py-3 border-b border-gray-200">
                <div class="flex space-x-2 overflow-x-auto">
                    {#each dateOptions as dateOption, index}
                        <button 
                            class="flex-shrink-0 px-3 py-2 rounded-lg text-center {selectedDateIndex === index ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}"
                            on:click={() => handleDateSelect(index)}
                        >
                            <div class="text-xs font-medium">{dateOption.day}</div>
                            <div class="text-sm font-bold">₹ {dateOption.price}</div>
                        </button>
                    {/each}
                </div>
            </div>

            <!-- Filter Controls -->
            <div class="bg-white px-4 py-3 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <button 
                        class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-full text-sm font-medium"
                        on:click={handleSortFilter}
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                        </svg>
                        Sort & Filter
                    </button>
                    
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
                        Cheapest
                    </button>
                    
                    <button 
                        class="flex items-center px-4 py-2 {showRefundableOnly ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'} rounded-full text-sm font-medium"
                        on:click={toggleRefundable}
                    >
                        Refundable
                        {#if showRefundableOnly}
                            <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        {/if}
                    </button>
                </div>
            </div>

            <!-- Compare and Fly Banner -->
            <div class="bg-green-100 px-4 py-3 border-b border-gray-200">
                <div class="flex items-center text-sm text-green-700">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    Compare and fly with ✈️ Goardia EaseMyTrip
                    <button class="ml-auto text-green-600">×</button>
                </div>
            </div>

            <!-- Flight List -->
            <div class="flex-1 overflow-y-auto">
                {#if flights.length === 0}
                    <div class="flex flex-col items-center justify-center py-8">
                        <p class="text-gray-500 text-center">No flights found for your search criteria</p>
                    </div>
                {:else}
                    {#each flights as flight}
                        <div class="bg-white mb-2 mx-4 mt-4 rounded-lg shadow-sm border border-gray-200">
                            <!-- Flight Card -->
                            <div class="p-4">
                                <!-- Airline and Tags -->
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-5 bg-red-600 rounded flex items-center justify-center mr-3">
                                            <span class="text-white text-xs font-bold">AA</span>
                                        </div>
                                        <span class="text-gray-700 font-medium">{flight.airline}</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        {#each flight.tags as tag}
                                            <span class="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded">
                                                {tag}
                                            </span>
                                        {/each}
                                    </div>
                                </div>

                                <!-- Flight Details -->
                                <div class="flex items-center justify-between mb-4">
                                    <!-- Departure -->
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-gray-900">{flight.departure}</div>
                                        <div class="text-sm text-gray-500">{flight.departureAirport}</div>
                                    </div>

                                    <!-- Duration -->
                                    <div class="flex-1 mx-4">
                                        <div class="text-center">
                                            <div class="text-xs text-gray-500 mb-1">{flight.duration}</div>
                                            <div class="border-t border-gray-300 relative">
                                                <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-600 rounded-full"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Arrival -->
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-gray-900">{flight.arrival}</div>
                                        <div class="text-sm text-gray-500">{flight.arrivalAirport}</div>
                                    </div>

                                    <!-- Price -->
                                    <div class="text-right ml-4">
                                        <div class="text-2xl font-bold text-green-600">₹ {flight.price.toLocaleString()}</div>
                                        <div class="text-xs text-gray-500">per adult</div>
                                    </div>
                                </div>

                                <!-- Offer Banner -->
                                {#if flight.offer}
                                    <div class="bg-green-50 border border-green-200 rounded-lg p-2 mb-3">
                                        <div class="flex items-center text-sm text-green-700">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                            {flight.offer}
                                        </div>
                                    </div>
                                {/if}
                            </div>

                            <!-- Select Button -->
                            <div class="border-t border-gray-200 px-4 py-3">
                                <button 
                                    class="w-full bg-blue-600 text-white font-semibold py-2 rounded-lg hover:bg-blue-700 transition-colors"
                                    on:click={() => handleFlightSelect(flight)}
                                >
                                    Select Flight
                                </button>
                            </div>
                        </div>
                    {/each}
                {/if}

                <!-- Today's Offer Section -->
                <div class="mx-4 my-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Today's Offer</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                            <div class="text-sm font-semibold text-gray-900 mb-1">FlyDisc10</div>
                            <div class="text-xs text-gray-500 mb-2">Flat 10% Cashback</div>
                            <button class="text-blue-600 text-xs font-medium">Copy ©</button>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                            <div class="text-sm font-semibold text-gray-900 mb-1">FlyDisc10</div>
                            <div class="text-xs text-gray-500 mb-2">Flat 10% Cash</div>
                            <button class="text-blue-600 text-xs font-medium">Copy ©</button>
                        </div>
                    </div>
                </div>

                <!-- Bottom padding -->
                <div class="h-4"></div>
            </div>
        </div>
    {/if}
</div>
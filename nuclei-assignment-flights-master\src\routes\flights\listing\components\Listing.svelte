<script lang="ts">
    import AppBar from "@CDNA-Technologies/svelte-vitals/components/appbar";
    import PrimaryLoader from "@CDNA-Technologies/svelte-vitals/components/primary-loader";
    import {
      ErrorHandling,
      lceStore,
      setContentLce,
      setLoadingLce,
    } from "@CDNA-Technologies/svelte-vitals/error-handling";
    import { NucleiLogger } from "@CDNA-Technologies/svelte-vitals/logger";
    import { page } from '$app/stores';
    import { onMount } from "svelte";
    import { ApiUtil } from '@CDNA-Technologies/svelte-vitals/api-util';


    // Get source and destination from page state or set defaults
    let fromCity = $page.state?.fromCity || { city: 'New Delhi', code: 'DEL' };
    let toCity = $page.state?.toCity || { city: 'Mumbai', code: 'BOM' };
    let travelDate = $page.state?.travelDate || '22 Dec';
    let travellers = $page.state?.travellers || 1;
    let classType = $page.state?.classType || 'Economy';

    console.log("data from Landing page: ", history.state);
    const state = history.state;
    const source = state.searchData.from.city;
    const dest = state.searchData.to.city;
    const sourceCode = state.searchData.from.code;
    const destCode = state.searchData.to.code;
    let appBarTitle = `(${sourceCode}) ${source} → (${destCode}) ${dest}`;

    const depart = state.searchData.departure;
    const pass = state.searchData.passengers;
    const clas = state.searchData.class;

    let appBarSubtitle = `${depart} | ${pass} Traveller${pass > 1 ? 's' : ''} | ${clas}`;

    console.log("Data From Landing Page: ", source, dest);
    // Mock flight data
    let flights = [
        {
            id: 1,
            airline: 'Air Asia',
            logo: 'https://via.placeholder.com/40x20/FF0000/FFFFFF?text=AA',
            departure: '23:35',
            arrival: '01:05',
            departureAirport: 'BLR',
            arrivalAirport: 'HYD',
            duration: 'Non-Stop',
            price: 2500,
            originalPrice: null,
            offer: 'Found ₹400 off - Tap to compare',
            tags: ['Refundable', 'Free Meal'],
            amenities: ['refundable', 'meal']
        },
        {
            id: 2,
            airline: 'Air Asia',
            logo: 'https://via.placeholder.com/40x20/FF0000/FFFFFF?text=AA',
            departure: '23:35',
            arrival: '01:05',
            departureAirport: 'BLR',
            arrivalAirport: 'HYD',
            duration: '2h 15m\nNon-Stop',
            price: 2500,
            originalPrice: null,
            offer: 'Found ₹400 off - Tap to compare',
            tags: ['Extra Baggage', 'Free Meal'],
            amenities: ['baggage', 'meal']
        }
    ];

    // Date options for the date selector
    let dateOptions = [
        { day: 'Tue, 27 Jun', price: 4807 },
        { day: 'Wed, 28 Jun', price: 5203 },
        { day: 'Thu, 29 Jun', price: 6100 },
        { day: 'Fri, 30 Ju', price: 4500 }
    ];

    let selectedDateIndex = 1; // Wed is selected by default
    let sortBy = 'cheapest';
    let showRefundableOnly = true;

    function handleSearchData(){
        const state = history.state;
        console.log('Navigation state:', state);
    }

       let searchData = $page.state?.searchData || {
        from: { 
            city: 'New Delhi', 
            code: 'DEL',
            airport: 'Indira Gandhi International Airport' 
        },
        to: { 
            city: 'Mumbai', 
            code: 'BOM',
            airport: 'Chhatrapati Shivaji International Airport' 
        },
        departure: '10 Aug',
        passengers: 1,
        class: 'Economy'
    };

    // Create AppBar title with route info using the actual search data

    onMount(async () => {
        NucleiLogger.logInfo("Flights", "Listing screen mounted");
        console.log('Page state:', $page.state);
        console.log('From City:', fromCity);
        console.log('To City:', toCity);
        handleSearchData();
        setLoadingLce();
       await fetchScreenData();
    });

    const fetchScreenData = async () => {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setContentLce();
    };

    function handleRetry() {
        setLoadingLce();
        fetchScreenData();
    }

    function handleDateSelect(index: number) {
        selectedDateIndex = index;
    }

    function handleSortFilter() {
        // Handle sort & filter functionality
        console.log('Sort & Filter clicked');
    }

    function toggleRefundable() {
        showRefundableOnly = !showRefundableOnly;
    }

    function handleFlightSelect(flight: any) {
        console.log('Selected flight:', flight);
        // Navigate to flight details or booking
    }
</script>

<div class="h-screen flex flex-col bg-gray-50">
    <!-- <AppBar title={appBarTitle} /> -->
     <AppBar>
	<div slot="title" class="flex flex-col leading-tight">
		<p class="pl-5 text-base font-bold">{appBarTitle}</p>
		<p class="pl-5 text-sm text-black-400">{appBarSubtitle}</p>
	</div>
</AppBar>

    {#if $lceStore.isLoading}
        <div class="h-screen flex flex-col justify-center">
            <PrimaryLoader />
        </div>
    {:else if $lceStore.hasError && $lceStore.errorDetails != null}
        <ErrorHandling
            errorHandling={$lceStore.errorDetails}
            on:submit={handleRetry}
        />
    {:else if $lceStore.hasContent}
        <div class="flex-1 overflow-hidden">
            <!-- Date Selector -->
            <div class="bg-white px-4 py-3 border-b border-gray-200">
                <div class="flex space-x-2 overflow-x-auto">
                    {#each dateOptions as dateOption, index}
                        <button 
                            class="flex-shrink-0 px-3 py-2 rounded-lg text-center {selectedDateIndex === index ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}"
                            on:click={() => handleDateSelect(index)}
                        >
                            <div class="text-xs font-medium">{dateOption.day}</div>
                            <div class="text-sm font-bold">₹ {dateOption.price}</div>
                        </button>
                    {/each}
                </div>
            </div>

            <!-- Filter Controls -->
            <div class="bg-white px-4 py-3 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <button 
                        class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-full text-sm font-medium"
                        on:click={handleSortFilter}
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                        </svg>
                        Sort & Filter
                    </button>
                    
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
                        Cheapest
                    </button>
                    
                    <button 
                        class="flex items-center px-4 py-2 {showRefundableOnly ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'} rounded-full text-sm font-medium"
                        on:click={toggleRefundable}
                    >
                        Refundable
                        {#if showRefundableOnly}
                            <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        {/if}
                    </button>
                </div>
            </div>

            <!-- Compare and Fly Banner -->
            <div class="bg-green-100 px-4 py-3 border-b border-gray-200">
                <div class="flex items-center text-sm text-green-700">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    Compare and fly with ✈️ Goardia EaseMyTrip
                    <button class="ml-auto text-green-600">×</button>
                </div>
            </div>

            <!-- Flight List -->
            <div class="flex-1 overflow-y-auto">
                {#each flights as flight}
                    <div class="bg-white mb-2 mx-4 mt-4 rounded-lg shadow-sm border border-gray-200">
                        <!-- Flight Card -->
                        <div class="p-4">
                            <!-- Airline and Tags -->
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-8 h-5 bg-red-600 rounded flex items-center justify-center mr-3">
                                        <span class="text-white text-xs font-bold">AA</span>
                                    </div>
                                    <span class="text-gray-700 font-medium">{flight.airline}</span>
                                </div>
                                <div class="flex space-x-2">
                                    {#each flight.tags as tag}
                                        <span class="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded">
                                            {tag}
                                        </span>
                                    {/each}
                                </div>
                            </div>

                            <!-- Flight Details -->
                            <div class="flex items-center justify-between mb-4">
                                <!-- Departure -->
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-900">{flight.departure}</div>
                                    <div class="text-sm text-gray-500">{flight.departureAirport}</div>
                                </div>

                                <!-- Duration -->
                                <div class="flex-1 mx-4">
                                    <div class="text-center">
                                        <div class="text-xs text-gray-500 mb-1">{flight.duration}</div>
                                        <div class="border-t border-gray-300 relative">
                                            <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-600 rounded-full"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Arrival -->
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-900">{flight.arrival}</div>
                                    <div class="text-sm text-gray-500">{flight.arrivalAirport}</div>
                                </div>

                                <!-- Price -->
                                <div class="text-right ml-4">
                                    <div class="text-2xl font-bold text-green-600">₹ {flight.price.toLocaleString()}</div>
                                    <div class="text-xs text-gray-500">per adult</div>
                                </div>
                            </div>

                            <!-- Offer Banner -->
                            {#if flight.offer}
                                <div class="bg-green-50 border border-green-200 rounded-lg p-2 mb-3">
                                    <div class="flex items-center text-sm text-green-700">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        {flight.offer}
                                    </div>
                                </div>
                            {/if}
                        </div>

                        <!-- Select Button -->
                        <div class="border-t border-gray-200 px-4 py-3">
                            <button 
                                class="w-full bg-blue-600 text-white font-semibold py-2 rounded-lg hover:bg-blue-700 transition-colors"
                                on:click={() => handleFlightSelect(flight)}
                            >
                                Select Flight
                            </button>
                        </div>
                    </div>
                {/each}

                <!-- Today's Offer Section -->
                <div class="mx-4 my-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Today's Offer</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                            <div class="text-sm font-semibold text-gray-900 mb-1">FlyDisc10</div>
                            <div class="text-xs text-gray-500 mb-2">Flat 10% Cashback</div>
                            <button class="text-blue-600 text-xs font-medium">Copy ©</button>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                            <div class="text-sm font-semibold text-gray-900 mb-1">FlyDisc10</div>
                            <div class="text-xs text-gray-500 mb-2">Flat 10% Cash</div>
                            <button class="text-blue-600 text-xs font-medium">Copy ©</button>
                        </div>
                    </div>
                </div>

                <!-- Additional Flight -->
                <div class="bg-white mb-4 mx-4 rounded-lg shadow-sm border border-gray-200">
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-8 h-5 bg-red-600 rounded flex items-center justify-center mr-3">
                                    <span class="text-white text-xs font-bold">AA</span>
                                </div>
                                <span class="text-gray-700 font-medium">Air Asia</span>
                            </div>
                            <span class="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded">Free Meal</span>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900">23:35</div>
                                <div class="text-sm text-gray-500">BLR</div>
                            </div>

                            <div class="flex-1 mx-4">
                                <div class="text-center">
                                    <div class="text-xs text-gray-500 mb-1">1 Stop</div>
                                    <div class="border-t border-gray-300 relative">
                                        <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-600 rounded-full"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900">01:05</div>
                                <div class="text-sm text-gray-500">HYD</div>
                            </div>

                            <div class="text-right ml-4">
                                <div class="text-2xl font-bold text-green-600">₹ 2,500</div>
                                <div class="text-xs text-gray-500">per adult</div>
                            </div>
                        </div>

                        <div class="bg-green-50 border border-green-200 rounded-lg p-2">
                            <div class="flex items-center text-sm text-green-700">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Found ₹400 off - Tap to compare
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bottom padding -->
                <div class="h-4"></div>
            </div>
        </div>
    {/if}
</div>
<!-- Compare and Fly Section -->
<div class="flex items-center font-roboto font-normal text-sm leading-4 text-[#676767] mb-2">
	Compare and fly:
	<span>
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
			<use href="/icons.svg#icon-cleartrip"></use>
		</svg>
	</span>
	<span>
		<svg xmlns="http://www.w3.org/2000/svg" width="46" height="18" viewBox="0 0 46 18" fill="none">
			<g clip-path="url(#clip0_32537_32102)">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M4.05501 14.0482V15.0953H0.320312V8.60641H3.91222V9.65034H1.60223V11.221H3.77895V12.2427H1.60223V14.0482H4.05501ZM4.99741 10.6975C5.31789 10.5134 5.9176 10.3008 6.67596 10.3008C8.17999 10.3008 8.58297 11.2305 8.58297 12.2586V13.9816C8.58297 14.3941 8.60201 14.8097 8.65278 15.0953H7.49461L7.41846 14.6067H7.39307C7.12336 14.9779 6.65057 15.1905 6.11115 15.1905C5.21952 15.1905 4.66106 14.54 4.66106 13.7785C4.66106 12.5727 5.76529 12.0079 7.30105 12.0079C7.30105 11.5415 7.13288 11.1893 6.43163 11.1893C5.99375 11.1893 5.55587 11.3225 5.24491 11.5098L4.99741 10.6975ZM7.33278 12.8075C6.63154 12.8075 5.91442 12.9662 5.91442 13.6516C5.91442 14.099 6.20317 14.2925 6.52365 14.2925C6.82509 14.2925 7.33278 14.0799 7.33278 13.4834V12.8075ZM9.68719 13.9625C9.93787 14.1053 10.4202 14.2735 10.8168 14.2735C11.2642 14.2735 11.4831 14.0895 11.4831 13.8007C11.4831 13.5151 11.3213 13.3628 10.7914 13.1439C9.8871 12.7885 9.56027 12.2903 9.56027 11.7763C9.56027 10.9323 10.252 10.2913 11.3308 10.2913C11.829 10.2913 12.251 10.4087 12.5271 10.5515L12.2828 11.4304C12.0733 11.3194 11.737 11.1861 11.3721 11.1861C10.985 11.1861 10.7756 11.3797 10.7756 11.6335C10.7756 11.8937 10.9501 12.0301 11.5085 12.2586C12.3272 12.5854 12.7048 13.0423 12.7048 13.6928C12.7048 14.5876 12.0067 15.1778 10.7978 15.1778C10.2679 15.1778 9.75383 15.0509 9.43335 14.8669L9.68719 13.9625ZM14.5293 13.1185C14.561 13.9181 15.1512 14.2386 15.8429 14.2386C16.3252 14.2386 16.6965 14.1561 17.0265 14.045L17.2105 14.9081C16.8139 15.0763 16.2491 15.1778 15.6494 15.1778C14.158 15.1778 13.3045 14.264 13.3045 12.8044C13.3045 11.4558 14.1073 10.2881 15.5225 10.2881C16.6965 10.2881 17.4644 11.1004 17.4644 12.6203C17.4644 12.8044 17.4549 12.9821 17.4295 13.1185H14.5293ZM16.2776 12.2491C16.284 11.7573 16.0809 11.1575 15.4495 11.1575C14.8339 11.1575 14.5737 11.7826 14.5388 12.2491H16.2776ZM24.5086 8.60006L24.8893 15.0921H23.6677L23.5408 12.5696C23.5059 11.716 23.4646 10.6689 23.471 10.0121H23.4488C23.252 10.7863 23.0172 11.6494 22.7888 12.3728L21.9701 15.0001H21.0023L20.2662 12.4173C20.0472 11.6811 19.8442 10.8053 19.6855 10.0121H19.6601C19.6443 10.8307 19.5998 11.7922 19.5586 12.5949L19.4317 15.0921H18.2608L18.7082 8.60006H20.2916L21.0087 11.0401C21.2276 11.8271 21.4212 12.5854 21.5671 13.3279H21.5925C21.7512 12.6013 21.9447 11.8175 22.1732 11.0401L22.9316 8.60006H24.5086ZM28.6177 10.3897H29.9599L28.862 13.6579C28.3226 15.1778 27.9164 16.0028 27.3707 16.5359C26.9042 16.9738 26.4061 17.1451 26.1205 17.1927L25.8 16.1393C26.0031 16.079 26.2474 15.9457 26.4759 15.7934C26.8979 15.4983 27.1168 15.016 27.1168 14.924C27.1168 14.8732 27.0978 14.8066 27.0661 14.7114L25.4383 10.3897H26.8471L27.5547 12.8456C27.6309 13.1344 27.7229 13.4644 27.7832 13.7246H27.8086C27.8657 13.4707 27.9418 13.1439 28.0116 12.8456L28.6177 10.3897ZM34.6814 8.60006V9.68207H33.0283V15.0921H31.7463V9.68207H30.0995V8.60006H34.6814ZM36.5218 11.2115C36.7503 10.6023 37.2738 10.2913 37.718 10.2913C37.8386 10.2913 37.9211 10.2976 38.0068 10.3167V11.5066C37.9137 11.4897 37.8037 11.4812 37.6768 11.4812C37.0961 11.4812 36.6487 11.8588 36.6487 12.6616V15.0921H35.3668V11.8588C35.3668 11.2781 35.3478 10.8117 35.3319 10.3897H36.4361L36.4869 11.2115H36.5218ZM39.3617 9.77409C38.9809 9.77409 38.6731 9.47582 38.6731 9.10775C38.6731 8.73967 38.9809 8.44141 39.3617 8.44141C39.7425 8.44141 40.0502 8.73967 40.0502 9.10775C40.0502 9.47582 39.7425 9.77409 39.3617 9.77409ZM38.7239 10.3897H40.0058V15.0921H38.7239V10.3897ZM42.3095 11.0306H42.3253C42.6395 10.5515 43.1376 10.2881 43.7595 10.2881C44.8162 10.2881 45.5999 11.2527 45.5999 12.6933C45.5999 14.4258 44.5877 15.1842 43.6072 15.1842C43.0773 15.1842 42.6712 14.9494 42.4618 14.6352H42.4459V16.9738H41.1608V11.9254C41.1608 11.3194 41.1449 10.8212 41.1291 10.3897H42.2492L42.3095 11.0306ZM42.4459 13.1915C42.4459 13.8769 42.8679 14.2037 43.3058 14.2037C43.9563 14.2037 44.2926 13.5818 44.2926 12.7377C44.2926 11.9 43.9626 11.294 43.3312 11.294C42.833 11.294 42.4459 11.7477 42.4459 12.3157V13.1915Z" fill="#006BB1"/>
				<path fill-rule="evenodd" clip-rule="evenodd" d="M8.62891 11.0019C12.1637 4.06242 21.8225 9.36461 27.1786 4.73828C21.8479 10.1579 11.2689 5.58232 8.62891 11.0019Z" fill="#007BB3"/>
				<path fill-rule="evenodd" clip-rule="evenodd" d="M21.1244 0.558458L33.6612 1.56432C34.2768 1.60557 34.1752 1.71663 33.8706 1.99586L27.7561 7.81843C27.4103 8.18968 27.3341 8.08179 26.928 7.65025L24.5323 5.01661C24.2118 4.69613 24.1611 4.6295 24.71 4.43277L30.6563 2.56701L23.9675 3.73469C23.463 3.83306 23.5296 3.90921 23.1742 3.42056L20.9404 0.939226C20.6707 0.644131 20.6865 0.491824 21.1244 0.558458Z" fill="#09B7E1"/>
				<path fill-rule="evenodd" clip-rule="evenodd" d="M24.7329 4.86506L25.3834 5.54092C25.6943 5.87092 25.6087 5.88679 25.2564 6.09621L23.1876 7.03544C22.9433 7.17823 22.7941 7.06082 22.9623 6.79111L24.2696 4.74766C24.387 4.49381 24.5235 4.63026 24.7329 4.86506Z" fill="#007BB3"/>
			</g>
			<defs>
				<clipPath id="clip0_32537_32102">
					<rect width="45.4155" height="16.6613" fill="white" transform="translate(0.320312 0.542969)"/>
				</clipPath>
			</defs>
		</svg>
	</span>
</div>
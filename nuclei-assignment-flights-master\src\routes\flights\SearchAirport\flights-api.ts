import { ApiUtil } from '@CDNA-Technologies/svelte-vitals/api-util';
import type { AirportSuggestionsResponse, AirportList } from '../path/to/your/messages'; // Update the path to your message file

// Define the Airport type to match your component's needs
export interface Airport {
    code: string;
    name: string;
    airport: string;
}

// Function to convert API response format to the Airport interface
function convertApiToAirport(apiAirport: AirportList): Airport {
    return {
        code: apiAirport.iataCode,
        name: apiAirport.city,
        airport: apiAirport.name
    };
}

// Function to fetch popular cities from the API
export async function getPopularCities(): Promise<Airport[]> {
    console.log('Calling getPopularCities API...');
    
    const endpoint = '/com.gonuclei.flights.v1.LandingService/getPopularCities';
    const requestBody = {};
    
    try {
        const response = await ApiUtil.post(endpoint, requestBody);
        
        if (response.hasError()) {
            console.error('getPopularCities failed:', response.error);
            // Return an empty array on error to prevent breaking the UI
            return [];
        } else {
            console.log('getPopularCities success:', response.response);
            console.log(`Got ${response.response?.airportList?.length || 0} airports`);
            // Convert and return the data in the expected format
            return response.response.airportList.map(convertApiToAirport);
        }
    } catch (error) {
        console.error('Unexpected error in getPopularCities:', error);
        return [];
    }
}
